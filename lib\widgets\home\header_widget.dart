import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:befine/theme/app_theme.dart';

class HeaderWidget extends StatefulWidget {
  const HeaderWidget({Key? key}) : super(key: key);

  @override
  State<HeaderWidget> createState() => _HeaderWidgetState();
}

class _HeaderWidgetState extends State<HeaderWidget>
    with WidgetsBindingObserver {
  Map<String, String> _userData = {};
  File? _profileImage;
  bool _notificationsEnabled = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadUserData();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Refresh data when app comes back to foreground
      _loadUserData();
    }
  }

  Future<void> refreshData() async {
    await _loadUserData();
  }

  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _userData = {
        'firstName': prefs.getString('patient_firstName') ?? 'Sami',
        'lastName': prefs.getString('patient_lastName') ?? 'Doe',
      };

      // Load profile image if exists
      String? imagePath = prefs.getString('patient_profileImagePath');
      if (imagePath != null && imagePath.isNotEmpty) {
        _profileImage = File(imagePath);
      }

      // Load notification setting
      _notificationsEnabled = prefs.getBool('notifications') ?? true;

      _isLoading = false;
    });
  }

  Future<void> _toggleNotifications() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _notificationsEnabled = !_notificationsEnabled;
    });

    await prefs.setBool('notifications', _notificationsEnabled);

    // Show feedback to user
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _notificationsEnabled
                ? 'Notifications enabled'
                : 'Notifications disabled',
          ),
          backgroundColor: _notificationsEnabled ? Colors.green : Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Row(
        children: [
          CircleAvatar(radius: 20, backgroundColor: Colors.grey),
          SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [Text('Loading...')],
          ),
          Spacer(),
          Icon(Icons.notifications_none_outlined, size: 28),
        ],
      );
    }

    return Row(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
          backgroundImage:
              _profileImage != null
                  ? FileImage(_profileImage!)
                  : const AssetImage('assets/icons/default_avatar.png')
                      as ImageProvider,
          child:
              _profileImage == null
                  ? Text(
                    _userData['firstName']!.isNotEmpty
                        ? _userData['firstName']!.substring(0, 1).toUpperCase()
                        : "U",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  )
                  : null,
        ),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Day 1', // Keep treatment day counter for future development
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).hintColor,
              ),
            ),
            Text(
              'Welcome ${_userData['firstName']}',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const Spacer(),
        IconButton(
          icon: Icon(
            _notificationsEnabled
                ? Icons.notifications_active
                : Icons.notifications_off,
            size: 28,
            color: _notificationsEnabled ? AppTheme.primaryColor : Colors.grey,
          ),
          onPressed: _toggleNotifications,
          tooltip:
              _notificationsEnabled
                  ? 'Disable notifications'
                  : 'Enable notifications',
        ),
      ],
    );
  }
}
