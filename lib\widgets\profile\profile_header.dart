import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:befine/pages/patient/edit_profile_screen.dart';
import 'package:befine/theme/app_theme.dart';

class ProfileHeader extends StatefulWidget {
  final String userRole;

  const ProfileHeader({Key? key, required this.userRole}) : super(key: key);

  @override
  State<ProfileHeader> createState() => _ProfileHeaderState();
}

class _ProfileHeaderState extends State<ProfileHeader> {
  Map<String, String> _userData = {};
  File? _profileImage;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _userData = {
        'firstName': prefs.getString('patient_firstName') ?? 'Sami',
        'lastName': prefs.getString('patient_lastName') ?? 'Doe',
        'email': prefs.getString('patient_email') ?? '<EMAIL>',
        'phoneNumber':
            prefs.getString('patient_phoneNumber') ?? '****** 567 8900',
        'address':
            prefs.getString('patient_address') ?? '123 Health St, Medical City',
        'dateOfBirth': prefs.getString('patient_dateOfBirth') ?? '1990-01-01',
        'height': prefs.getString('patient_height') ?? '175',
        'weight': prefs.getString('patient_weight') ?? '70',
        'emergencyContact':
            prefs.getString('patient_emergencyContact') ?? '****** 654 3210',
        'notes': prefs.getString('patient_notes') ?? 'No allergies',
        'gender': prefs.getString('patient_gender') ?? 'male',
        'smokingStatus':
            prefs.getString('patient_smokingStatus') ?? 'Never smoked',
      };

      // Load profile image if exists
      String? imagePath = prefs.getString('patient_profileImagePath');
      if (imagePath != null && imagePath.isNotEmpty) {
        _profileImage = File(imagePath);
      }

      _isLoading = false;
    });
  }

  String _calculateAge(String dateOfBirth) {
    if (dateOfBirth.isEmpty) return 'Unknown';

    try {
      DateTime birthDate = DateTime.parse(dateOfBirth);
      DateTime today = DateTime.now();
      int age = today.year - birthDate.year;

      if (today.month < birthDate.month ||
          (today.month == birthDate.month && today.day < birthDate.day)) {
        age--;
      }

      return '$age years';
    } catch (e) {
      return 'Unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        margin: const EdgeInsets.all(16.0),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Center(
          child: CircularProgressIndicator(color: AppTheme.primaryColor),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.1),
            AppTheme.primaryColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Image and Basic Info
          Row(
            children: [
              Stack(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: AppTheme.primaryColor.withValues(
                      alpha: 0.2,
                    ),
                    backgroundImage:
                        _profileImage != null
                            ? FileImage(_profileImage!)
                            : null,
                    child:
                        _profileImage == null
                            ? Text(
                              _userData['firstName']!.isNotEmpty
                                  ? _userData['firstName']!
                                      .substring(0, 1)
                                      .toUpperCase()
                                  : "U",
                              style: TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.primaryColor,
                              ),
                            )
                            : null,
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: () async {
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const EditProfileScreen(),
                          ),
                        );
                        if (result == true) {
                          _loadUserData(); // Refresh data
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const Icon(
                          Icons.edit,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${_userData['firstName']} ${_userData['lastName']}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _userData['email']!,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _userData['gender']!.toUpperCase(),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Quick Stats Row
          Row(
            children: [
              Expanded(
                child: _buildQuickStat(
                  icon: Icons.cake,
                  label: 'Age',
                  value: _calculateAge(_userData['dateOfBirth']!),
                ),
              ),
              Expanded(
                child: _buildQuickStat(
                  icon: Icons.height,
                  label: 'Height',
                  value:
                      _userData['height']!.isNotEmpty
                          ? '${_userData['height']} cm'
                          : 'Not set',
                ),
              ),
              Expanded(
                child: _buildQuickStat(
                  icon: Icons.monitor_weight,
                  label: 'Weight',
                  value:
                      _userData['weight']!.isNotEmpty
                          ? '${_userData['weight']} kg'
                          : 'Not set',
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Additional Info Row
          Row(
            children: [
              Expanded(
                child: _buildQuickStat(
                  icon: Icons.smoking_rooms,
                  label: 'Smoking',
                  value: _userData['smokingStatus']!,
                ),
              ),
              Expanded(
                child: _buildQuickStat(
                  icon: Icons.phone,
                  label: 'Phone',
                  value:
                      _userData['phoneNumber']!.isNotEmpty
                          ? _userData['phoneNumber']!
                          : 'Not set',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStat({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.1)),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppTheme.primaryColor, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: AppTheme.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 11,
              color: AppTheme.textPrimaryColor,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
